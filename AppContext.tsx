
import React, { createContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { User, Post, Event, UserRole, AppContextType, PostCategory, SocialLinks, SiteSettings, SystemHealth, Announcement, ContentStats } from './types';

// Fix: Export AppContext so it can be imported by other modules.
export const AppContext = createContext<AppContextType | undefined>(undefined);

const initialUsers: User[] = [
  { id: 'admin1', email: '<EMAIL>', password: 'admin123', role: UserRole.ADMIN, displayName: 'Admin User', profilePhotoUrl: 'https://picsum.photos/seed/admin/200/200', bannerImageUrl: 'https://picsum.photos/seed/admin-banner/1200/400', bio: 'Platform administrator with full access.', tags: ['Administrator', 'Platform Manager'], socialLinks: {}, isActive: true, createdAt: new Date().toISOString(), lastLoginAt: new Date().toISOString() },
  { id: 'user1', email: '<EMAIL>', password: 'password', role: UserRole.ARTIST, displayName: 'Artistic Annie', profilePhotoUrl: 'https://picsum.photos/seed/annie/200/200', bannerImageUrl: 'https://picsum.photos/seed/annie-banner/1200/400', bio: 'Passionate painter and sculptor exploring modern themes.', location: 'New York, NY', tags: ['Oil Painter', 'Abstract', 'Modern Art'], socialLinks: { instagram: 'https://instagram.com/annie', youtube: 'https://youtube.com/annie'}, artistStatement: 'My art explores the intersection of nature and urban life, creating vibrant pieces that challenge traditional perspectives.', genre: ['Abstract', 'Impressionism'], isActive: true, createdAt: new Date(Date.now() - 86400000 * 30).toISOString() },
  { id: 'user2', email: '<EMAIL>', password: 'password', role: UserRole.EDUCATOR, displayName: 'Musical Mark', profilePhotoUrl: 'https://picsum.photos/seed/mark/200/200', bannerImageUrl: 'https://picsum.photos/seed/mark-banner/1200/400', bio: 'Experienced guitar teacher for all levels with 15+ years of experience.', location: 'Los Angeles, CA', tags: ['Guitar Coach', 'Music Theory', 'Rock'], socialLinks: { youtube: 'https://youtube.com/mark', soundcloud: 'https://soundcloud.com/mark'}, lessonsOffered: [{id: 'lesson1', title: 'Beginner Guitar', description: 'Learn the basics of guitar playing.', duration: '60 minutes', price: '$50'}, {id: 'lesson2', title: 'Advanced Blues', description: 'Master blues techniques and improvisation.', duration: '90 minutes', price: '$75'}], isActive: true, createdAt: new Date(Date.now() - 86400000 * 45).toISOString()},
  { id: 'user3', email: '<EMAIL>', password: 'password', role: UserRole.FAN, displayName: 'Creative Chris', profilePhotoUrl: 'https://picsum.photos/seed/chris/200/200', bannerImageUrl: 'https://picsum.photos/seed/chris-banner/1200/400', bio: 'Loves discovering new artists and music. Always looking for the next big thing!', location: 'Chicago, IL', tags: ['Music Lover', 'Art Enthusiast'], socialLinks: { instagram: 'https://instagram.com/chris'}, followedArtistIds: ['user1', 'user4'], savedPostIds: ['post1', 'post3'], rsvpedEventIds: ['event1', 'event2'], isActive: true, createdAt: new Date(Date.now() - 86400000 * 20).toISOString() },
  { id: 'user4', email: '<EMAIL>', password: 'password', role: UserRole.ARTIST, displayName: 'Jazz Jasmine', profilePhotoUrl: 'https://picsum.photos/seed/jasmine/200/200', bannerImageUrl: 'https://picsum.photos/seed/jasmine-banner/1200/400', bio: 'Professional jazz vocalist and songwriter with a passion for improvisation.', location: 'Nashville, TN', tags: ['Jazz', 'Vocalist', 'Songwriter'], socialLinks: { spotify: 'https://spotify.com/jasmine', soundcloud: 'https://soundcloud.com/jasmine', instagram: 'https://instagram.com/jazzjasmine'}, artistStatement: 'Jazz is the language of the soul. Through my music, I aim to connect hearts and minds across all boundaries.', genre: ['Jazz', 'Blues', 'Soul'], isActive: true, createdAt: new Date(Date.now() - 86400000 * 60).toISOString() },
  { id: 'user5', email: '<EMAIL>', password: 'password', role: UserRole.EDUCATOR, displayName: 'Piano Patricia', profilePhotoUrl: 'https://picsum.photos/seed/patricia/200/200', bannerImageUrl: 'https://picsum.photos/seed/patricia-banner/1200/400', bio: 'Classical piano instructor specializing in technique and music theory.', location: 'Boston, MA', tags: ['Piano', 'Classical', 'Music Theory'], socialLinks: { youtube: 'https://youtube.com/patricia'}, lessonsOffered: [{id: 'lesson3', title: 'Piano Fundamentals', description: 'Build a strong foundation in piano technique.', duration: '45 minutes', price: '$60'}, {id: 'lesson4', title: 'Classical Repertoire', description: 'Learn beautiful classical pieces.', duration: '60 minutes', price: '$80'}], isActive: true, createdAt: new Date(Date.now() - 86400000 * 25).toISOString()},
  { id: 'user6', email: '<EMAIL>', password: 'password', role: UserRole.FAN, displayName: 'Melody Mike', profilePhotoUrl: 'https://picsum.photos/seed/mike/200/200', bannerImageUrl: 'https://picsum.photos/seed/mike-banner/1200/400', bio: 'Retired music teacher who loves supporting young artists.', location: 'Portland, OR', tags: ['Retired Educator', 'Music Supporter'], socialLinks: {}, followedArtistIds: ['user4'], savedPostIds: ['post2'], rsvpedEventIds: ['event3'], isActive: true, createdAt: new Date(Date.now() - 86400000 * 10).toISOString() },
];

const initialPosts: Post[] = [
  { id: 'post1', userId: 'user1', title: 'Sunset Overdrive', description: 'My latest abstract piece, inspired by city sunsets. This painting captures the vibrant energy of urban life as day transitions to night.', mediaUrl: 'https://picsum.photos/seed/sunsetart/600/400', mediaType: 'image', category: PostCategory.VISUAL_ART, tags: ['Abstract', 'Painting', 'Cityscape'], likes: 15, createdAt: new Date(Date.now() - 86400000 * 2).toISOString() },
  { id: 'post2', userId: 'user1', title: 'Melody in Motion', description: 'A short original composition exploring the relationship between movement and sound.', mediaUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3', mediaType: 'soundcloud', category: PostCategory.MUSIC, tags: ['Original', 'Piano', 'Instrumental'], likes: 22, createdAt: new Date(Date.now() - 86400000 * 1).toISOString() },
  { id: 'post3', userId: 'user4', title: 'Blue Note Sessions', description: 'Recording from last night\'s intimate jazz session. The magic happens when musicians connect on a deeper level.', mediaUrl: 'https://picsum.photos/seed/jazzstudio/600/400', mediaType: 'image', category: PostCategory.MUSIC, tags: ['Jazz', 'Live Performance', 'Blue Note'], likes: 31, createdAt: new Date(Date.now() - 86400000 * 3).toISOString() },
  { id: 'post4', userId: 'user4', title: 'Autumn Serenade', description: 'A new original piece inspired by the changing seasons. Available on all streaming platforms.', mediaUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3', mediaType: 'soundcloud', category: PostCategory.MUSIC, tags: ['Original', 'Jazz', 'Seasonal'], likes: 18, createdAt: new Date(Date.now() - 86400000 * 5).toISOString() },
  { id: 'post5', userId: 'user1', title: 'Urban Landscapes Series', description: 'Part 3 of my urban landscapes series - exploring the hidden beauty in everyday city scenes.', mediaUrl: 'https://picsum.photos/seed/urbanart/600/400', mediaType: 'image', category: PostCategory.VISUAL_ART, tags: ['Series', 'Urban', 'Photography'], likes: 27, createdAt: new Date(Date.now() - 86400000 * 7).toISOString() },
];

const initialEvents: Event[] = [
  { id: 'event1', educatorId: 'user2', title: 'Guitar Workshop: Blues Riffs', description: 'Learn classic blues guitar riffs and techniques. All levels welcome! We\'ll cover essential blues patterns, bending techniques, and how to create your own blues solos.', dateTime: new Date(Date.now() + 86400000 * 7).toISOString(), format: 'virtual', locationOrLink: 'Zoom Link Provided on RSVP', price: '$20', rsvps: ['user3', 'user6'], createdAt: new Date(Date.now() - 86400000 * 5).toISOString() },
  { id: 'event2', educatorId: 'user5', title: 'Classical Piano Masterclass', description: 'An intensive masterclass focusing on classical piano technique and interpretation. Perfect for intermediate to advanced players looking to refine their skills.', dateTime: new Date(Date.now() + 86400000 * 14).toISOString(), format: 'in-person', locationOrLink: 'Boston Conservatory, Room 201', price: '$75', rsvps: ['user3'], createdAt: new Date(Date.now() - 86400000 * 3).toISOString() },
  { id: 'event3', educatorId: 'user2', title: 'Songwriting Circle', description: 'Join fellow musicians in a collaborative songwriting session. Bring your ideas and let\'s create something beautiful together!', dateTime: new Date(Date.now() + 86400000 * 21).toISOString(), format: 'in-person', locationOrLink: 'Music Studio LA, 123 Sunset Blvd', price: 'Free', rsvps: ['user6', 'user4'], createdAt: new Date(Date.now() - 86400000 * 1).toISOString() },
  { id: 'event4', educatorId: 'user5', title: 'Music Theory Fundamentals', description: 'A comprehensive introduction to music theory covering scales, chords, and harmonic progressions. Great for beginners and those looking to strengthen their foundation.', dateTime: new Date(Date.now() + 86400000 * 28).toISOString(), format: 'virtual', locationOrLink: 'Google Meet Link Provided', price: '$35', rsvps: [], createdAt: new Date(Date.now() - 86400000 * 2).toISOString() },
  { id: 'event5', educatorId: 'user2', title: 'Advanced Guitar Techniques', description: 'Take your guitar playing to the next level with advanced techniques including sweep picking, tapping, and complex chord progressions.', dateTime: new Date(Date.now() - 86400000 * 7).toISOString(), format: 'virtual', locationOrLink: 'Zoom Recording Available', price: '$45', rsvps: ['user3', 'user6'], createdAt: new Date(Date.now() - 86400000 * 14).toISOString() },
];

const initialSiteSettings: SiteSettings = {
  siteName: 'The MusicArt Club',
  siteDescription: 'A vibrant creative community where music and art enthusiasts connect, share, and grow together.',
  contactEmail: '<EMAIL>',
  maintenanceMode: false,
  allowRegistration: true,
  featuredPostsLimit: 5,
  theme: {
    primaryColor: '#f97316',
    secondaryColor: '#8b5cf6',
    accentColor: '#06b6d4'
  }
};

const initialAnnouncements: Announcement[] = [
  {
    id: 'announcement1',
    title: 'Welcome to The MusicArt Club!',
    message: 'We\'re excited to have you join our creative community. Explore, create, and connect with fellow artists and music lovers.',
    type: 'success',
    isActive: true,
    targetRoles: [],
    createdAt: new Date().toISOString()
  }
];

export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>(initialUsers);
  const [posts, setPosts] = useState<Post[]>(initialPosts);
  const [events, setEvents] = useState<Event[]>(initialEvents);
  const [siteSettings, setSiteSettings] = useState<SiteSettings>(initialSiteSettings);
  const [announcements, setAnnouncements] = useState<Announcement[]>(initialAnnouncements);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const storedUser = localStorage.getItem('currentUser');
    const storedUsers = localStorage.getItem('mac_users');
    const storedPosts = localStorage.getItem('mac_posts');
    const storedEvents = localStorage.getItem('mac_events');
    const storedSettings = localStorage.getItem('mac_siteSettings');
    const storedAnnouncements = localStorage.getItem('mac_announcements');

    if (storedUser) setCurrentUser(JSON.parse(storedUser));

    // Always ensure admin user exists
    if (storedUsers) {
      const parsedUsers = JSON.parse(storedUsers);
      const hasAdmin = parsedUsers.some((u: User) => u.role === UserRole.ADMIN);
      if (!hasAdmin) {
        // Add admin user if it doesn't exist
        const adminUser = initialUsers.find(u => u.role === UserRole.ADMIN);
        if (adminUser) {
          parsedUsers.unshift(adminUser);
        }
      }
      setUsers(parsedUsers);
    } else {
      setUsers(initialUsers);
    }

    if (storedPosts) setPosts(JSON.parse(storedPosts)); else setPosts(initialPosts);
    if (storedEvents) setEvents(JSON.parse(storedEvents)); else setEvents(initialEvents);

    if (storedSettings) setSiteSettings(JSON.parse(storedSettings));
    else setSiteSettings(initialSiteSettings);

    if (storedAnnouncements) setAnnouncements(JSON.parse(storedAnnouncements));
    else setAnnouncements(initialAnnouncements);

    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (!isInitialized) return;
    localStorage.setItem('currentUser', JSON.stringify(currentUser));
  }, [currentUser, isInitialized]);

  useEffect(() => {
    if (!isInitialized) return;
    localStorage.setItem('mac_users', JSON.stringify(users));
  }, [users, isInitialized]);

  useEffect(() => {
    if (!isInitialized) return;
    localStorage.setItem('mac_posts', JSON.stringify(posts));
  }, [posts, isInitialized]);

  useEffect(() => {
    if (!isInitialized) return;
    localStorage.setItem('mac_events', JSON.stringify(events));
  }, [events, isInitialized]);

  useEffect(() => {
    if (!isInitialized) return;
    localStorage.setItem('mac_siteSettings', JSON.stringify(siteSettings));
  }, [siteSettings, isInitialized]);

  useEffect(() => {
    if (!isInitialized) return;
    localStorage.setItem('mac_announcements', JSON.stringify(announcements));
  }, [announcements, isInitialized]);


  const login = useCallback(async (email: string, password_param: string): Promise<User | null> => {
    console.log('Login attempt:', { email, password_param });
    console.log('Available users:', users.map(u => ({ email: u.email, role: u.role })));

    const user = users.find(u => u.email === email && u.password === password_param);
    if (user) {
      const updatedUser = { ...user, lastLoginAt: new Date().toISOString() };
      setUsers(prevUsers => prevUsers.map(u => u.id === user.id ? updatedUser : u));
      setCurrentUser(updatedUser);
      return updatedUser;
    }
    alert('Invalid credentials');
    return null;
  }, [users]);

  const signup = useCallback(async (userData: Omit<User, 'id' | 'profilePhotoUrl' | 'bannerImageUrl' | 'tags' | 'socialLinks'> & Partial<Pick<User, 'tags' | 'socialLinks'>>): Promise<User | null> => {
    if (users.find(u => u.email === userData.email)) {
      alert('User already exists with this email.');
      return null;
    }
    const newUser: User = {
      id: `user${Date.now()}`,
      ...userData,
      profilePhotoUrl: `https://picsum.photos/seed/${userData.displayName.split(' ')[0]}/200/200`,
      bannerImageUrl: `https://picsum.photos/seed/${userData.displayName.split(' ')[0]}-banner/1200/400`,
      tags: userData.tags || [],
      socialLinks: userData.socialLinks || {},
      lessonsOffered: userData.role === UserRole.EDUCATOR ? [] : undefined,
      followedArtistIds: userData.role === UserRole.FAN ? [] : undefined,
      savedPostIds: userData.role === UserRole.FAN ? [] : undefined,
      rsvpedEventIds: userData.role === UserRole.FAN ? [] : undefined,
      isActive: true,
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString(),
    };
    setUsers(prev => [...prev, newUser]);
    setCurrentUser(newUser);
    return newUser;
  }, [users]);

  const logout = useCallback(() => {
    setCurrentUser(null);
  }, []);

  const updateUserProfile = useCallback(async (userId: string, data: Partial<User>): Promise<User | null> => {
    let updatedUser: User | null = null;
    setUsers(prevUsers => prevUsers.map(u => {
      if (u.id === userId) {
        updatedUser = { ...u, ...data };
        return updatedUser;
      }
      return u;
    }));
    if (updatedUser && currentUser?.id === userId) {
      setCurrentUser(updatedUser);
    }
    return updatedUser;
  }, [currentUser]);

  const getUserById = useCallback((userId: string): User | null => {
    return users.find(u => u.id === userId) || null;
  }, [users]);

  const createPost = useCallback(async (postData: Omit<Post, 'id' | 'likes' | 'createdAt'>): Promise<Post | null> => {
    if (!currentUser || currentUser.role !== UserRole.ARTIST) {
        alert("Only artists can create posts.");
        return null;
    }
    const newPost: Post = {
      ...postData,
      id: `post${Date.now()}`,
      likes: 0,
      createdAt: new Date().toISOString(),
    };
    setPosts(prev => [newPost, ...prev]);
    return newPost;
  }, [currentUser]);

  const getPosts = useCallback((filters?: { category?: PostCategory; userId?: string }): Post[] => {
    return posts
      .filter(p => (!filters?.category || p.category === filters.category))
      .filter(p => (!filters?.userId || p.userId === filters.userId))
      .sort((a,b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [posts]);

  const createEvent = useCallback(async (eventData: Omit<Event, 'id' | 'rsvps' | 'createdAt'>): Promise<Event | null> => {
    if (!currentUser || currentUser.role !== UserRole.EDUCATOR) {
        alert("Only educators can create events.");
        return null;
    }
    const newEvent: Event = {
      ...eventData,
      id: `event${Date.now()}`,
      rsvps: [],
      createdAt: new Date().toISOString(),
    };
    setEvents(prev => [newEvent, ...prev]);
    return newEvent;
  }, [currentUser]);

  const getEvents = useCallback((filters?: { educatorId?: string }): Event[] => {
    return events
      .filter(e => (!filters?.educatorId || e.educatorId === filters.educatorId))
      .sort((a,b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [events]);

  const rsvpToEvent = useCallback(async (eventId: string, userId: string): Promise<boolean> => {
    setEvents(prevEvents => prevEvents.map(event => {
      if (event.id === eventId) {
        const rsvped = event.rsvps.includes(userId);
        return {
          ...event,
          rsvps: rsvped ? event.rsvps.filter(id => id !== userId) : [...event.rsvps, userId]
        };
      }
      return event;
    }));
    // Update user's RSVP list
    updateUserProfile(userId, {
      rsvpedEventIds: currentUser?.rsvpedEventIds?.includes(eventId)
        ? currentUser.rsvpedEventIds.filter(id => id !== eventId)
        : [...(currentUser?.rsvpedEventIds || []), eventId]
    });
    return true;
  }, [updateUserProfile, currentUser]);

  const followArtist = useCallback(async (fanId: string, artistId: string): Promise<boolean> => {
    updateUserProfile(fanId, {
      followedArtistIds: currentUser?.followedArtistIds?.includes(artistId)
        ? currentUser.followedArtistIds.filter(id => id !== artistId)
        : [...(currentUser?.followedArtistIds || []), artistId]
    });
    return true;
  }, [updateUserProfile, currentUser]);

  const savePost = useCallback(async (fanId: string, postId: string): Promise<boolean> => {
     updateUserProfile(fanId, {
      savedPostIds: currentUser?.savedPostIds?.includes(postId)
        ? currentUser.savedPostIds.filter(id => id !== postId)
        : [...(currentUser?.savedPostIds || []), postId]
    });
    // Also update post's like count (simplified)
    setPosts(prevPosts => prevPosts.map(p => {
      if (p.id === postId) {
        return { ...p, likes: currentUser?.savedPostIds?.includes(postId) ? p.likes - 1 : p.likes + 1 };
      }
      return p;
    }));
    return true;
  }, [updateUserProfile, currentUser]);

  // Admin functions
  const deleteUser = useCallback(async (userId: string): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      alert('Only admins can delete users.');
      return false;
    }
    if (userId === currentUser.id) {
      alert('Cannot delete your own admin account.');
      return false;
    }
    setUsers(prevUsers => prevUsers.filter(u => u.id !== userId));
    // Also remove user's posts and events
    setPosts(prevPosts => prevPosts.filter(p => p.userId !== userId));
    setEvents(prevEvents => prevEvents.filter(e => e.educatorId !== userId));
    return true;
  }, [currentUser]);

  const updateUserRole = useCallback(async (userId: string, newRole: UserRole): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      alert('Only admins can update user roles.');
      return false;
    }
    if (userId === currentUser.id && newRole !== UserRole.ADMIN) {
      alert('Cannot change your own admin role.');
      return false;
    }
    setUsers(prevUsers => prevUsers.map(u =>
      u.id === userId ? { ...u, role: newRole } : u
    ));
    return true;
  }, [currentUser]);

  const toggleUserStatus = useCallback(async (userId: string): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      alert('Only admins can toggle user status.');
      return false;
    }
    if (userId === currentUser.id) {
      alert('Cannot deactivate your own admin account.');
      return false;
    }
    setUsers(prevUsers => prevUsers.map(u =>
      u.id === userId ? { ...u, isActive: !u.isActive } : u
    ));
    return true;
  }, [currentUser]);

  const deletePost = useCallback(async (postId: string): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      alert('Only admins can delete posts.');
      return false;
    }
    setPosts(prevPosts => prevPosts.filter(p => p.id !== postId));
    return true;
  }, [currentUser]);

  const deleteEvent = useCallback(async (eventId: string): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      alert('Only admins can delete events.');
      return false;
    }
    setEvents(prevEvents => prevEvents.filter(e => e.id !== eventId));
    return true;
  }, [currentUser]);

  const getUserStats = useCallback(() => {
    const totalUsers = users.length;
    const activeUsers = users.filter(u => u.isActive !== false).length;
    const usersByRole = users.reduce((acc, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1;
      return acc;
    }, {} as Record<UserRole, number>);

    return { totalUsers, activeUsers, usersByRole };
  }, [users]);

  // Enhanced admin functions
  const getSiteSettings = useCallback(() => {
    return siteSettings;
  }, [siteSettings]);

  const updateSiteSettings = useCallback(async (settings: Partial<SiteSettings>): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      alert('Only admins can update site settings.');
      return false;
    }
    setSiteSettings(prev => ({ ...prev, ...settings }));
    return true;
  }, [currentUser]);

  const getSystemHealth = useCallback((): SystemHealth => {
    const now = new Date();
    const startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24 hours ago

    return {
      uptime: 24, // Simulated uptime in hours
      memoryUsage: Math.floor(Math.random() * 30) + 40, // Random between 40-70%
      activeUsers: users.filter(u => u.isActive !== false).length,
      errorCount: Math.floor(Math.random() * 5), // Random errors
      lastBackup: new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString() // 6 hours ago
    };
  }, [users]);

  const getContentStats = useCallback((): ContentStats => {
    const now = new Date();
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const postsThisWeek = posts.filter(post => new Date(post.createdAt) > lastWeek).length;
    const eventsThisWeek = events.filter(event =>
      event.createdAt && new Date(event.createdAt) > lastWeek
    ).length;

    const topCategories = Object.values(PostCategory).map(category => ({
      category,
      count: posts.filter(post => post.category === category).length
    })).sort((a, b) => b.count - a.count);

    const totalLikes = posts.reduce((sum, post) => sum + post.likes, 0);
    const engagementRate = posts.length > 0 ? (totalLikes / posts.length) : 0;

    return {
      totalPosts: posts.length,
      totalEvents: events.length,
      postsThisWeek,
      eventsThisWeek,
      topCategories,
      engagementRate
    };
  }, [posts, events]);

  const createAnnouncement = useCallback(async (announcementData: Omit<Announcement, 'id' | 'createdAt'>): Promise<Announcement | null> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      alert('Only admins can create announcements.');
      return null;
    }

    const newAnnouncement: Announcement = {
      ...announcementData,
      id: `announcement${Date.now()}`,
      createdAt: new Date().toISOString()
    };

    setAnnouncements(prev => [newAnnouncement, ...prev]);
    return newAnnouncement;
  }, [currentUser]);

  const getAnnouncements = useCallback(() => {
    return announcements.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [announcements]);

  const updateAnnouncement = useCallback(async (id: string, data: Partial<Announcement>): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      alert('Only admins can update announcements.');
      return false;
    }

    setAnnouncements(prev => prev.map(announcement =>
      announcement.id === id ? { ...announcement, ...data } : announcement
    ));
    return true;
  }, [currentUser]);

  const deleteAnnouncement = useCallback(async (id: string): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      alert('Only admins can delete announcements.');
      return false;
    }

    setAnnouncements(prev => prev.filter(announcement => announcement.id !== id));
    return true;
  }, [currentUser]);

  const bulkDeletePosts = useCallback(async (postIds: string[]): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      alert('Only admins can bulk delete posts.');
      return false;
    }

    setPosts(prev => prev.filter(post => !postIds.includes(post.id)));
    return true;
  }, [currentUser]);

  const bulkDeleteEvents = useCallback(async (eventIds: string[]): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      alert('Only admins can bulk delete events.');
      return false;
    }

    setEvents(prev => prev.filter(event => !eventIds.includes(event.id)));
    return true;
  }, [currentUser]);

  const bulkUpdateUsers = useCallback(async (userIds: string[], updates: Partial<User>): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      alert('Only admins can bulk update users.');
      return false;
    }

    setUsers(prev => prev.map(user =>
      userIds.includes(user.id) ? { ...user, ...updates } : user
    ));
    return true;
  }, [currentUser]);

  const exportUserData = useCallback(() => {
    const exportData = {
      users: users.map(user => ({
        ...user,
        password: undefined // Don't export passwords
      })),
      exportDate: new Date().toISOString(),
      totalUsers: users.length
    };
    return JSON.stringify(exportData, null, 2);
  }, [users]);

  const exportContentData = useCallback(() => {
    const exportData = {
      posts,
      events,
      exportDate: new Date().toISOString(),
      totalPosts: posts.length,
      totalEvents: events.length
    };
    return JSON.stringify(exportData, null, 2);
  }, [posts, events]);

  // Debug function to reset data (useful for development)
  const resetToInitialData = useCallback(() => {
    localStorage.removeItem('currentUser');
    localStorage.removeItem('mac_users');
    localStorage.removeItem('mac_posts');
    localStorage.removeItem('mac_events');
    localStorage.removeItem('mac_siteSettings');
    localStorage.removeItem('mac_announcements');
    setCurrentUser(null);
    setUsers(initialUsers);
    setPosts(initialPosts);
    setEvents(initialEvents);
    setSiteSettings(initialSiteSettings);
    setAnnouncements(initialAnnouncements);
  }, []);

  return (
    <AppContext.Provider value={{
        currentUser, users, posts, events,
        login, signup, logout, updateUserProfile, getUserById,
        createPost, getPosts, createEvent, getEvents,
        rsvpToEvent, followArtist, savePost,
        deleteUser, updateUserRole, toggleUserStatus, deletePost, deleteEvent, getUserStats, resetToInitialData,
        // Enhanced admin functions
        getSiteSettings, updateSiteSettings, getSystemHealth, getContentStats,
        createAnnouncement, getAnnouncements, updateAnnouncement, deleteAnnouncement,
        bulkDeletePosts, bulkDeleteEvents, bulkUpdateUsers, exportUserData, exportContentData
    }}>
      {isInitialized ? children : <div className="flex items-center justify-center h-screen"><SpinnerIcon className="w-12 h-12 text-primary" /></div>}
    </AppContext.Provider>
  );
};

export const useApp = (): AppContextType => {
  const context = React.useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

const SpinnerIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={`animate-spin h-5 w-5 ${className}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
  </svg>
);
