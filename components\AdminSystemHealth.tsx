import React, { useState, useEffect } from 'react';
import { useApp } from '../AppContext';
import { SystemHealth } from '../types';
import { Button, Card, Icon } from '../components';

interface AdminSystemHealthProps {
  className?: string;
}

export const AdminSystemHealth: React.FC<AdminSystemHealthProps> = ({ className = '' }) => {
  const { getSystemHealth, resetToInitialData } = useApp();
  const [systemHealth, setSystemHealth] = useState<SystemHealth>(getSystemHealth());
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showResetConfirm, setShowResetConfirm] = useState(false);

  // Refresh system health data
  const refreshHealth = async () => {
    setIsRefreshing(true);
    // Simulate API call delay
    setTimeout(() => {
      setSystemHealth(getSystemHealth());
      setIsRefreshing(false);
    }, 1000);
  };

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setSystemHealth(getSystemHealth());
    }, 30000);

    return () => clearInterval(interval);
  }, [getSystemHealth]);

  const handleReset = () => {
    resetToInitialData();
    setShowResetConfirm(false);
    refreshHealth();
  };

  const formatUptime = (hours: number) => {
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days}d ${remainingHours}h`;
  };

  const getHealthStatus = () => {
    if (systemHealth.errorCount > 10) return { status: 'critical', color: 'red' };
    if (systemHealth.errorCount > 5 || systemHealth.memoryUsage > 80) return { status: 'warning', color: 'yellow' };
    return { status: 'healthy', color: 'green' };
  };

  const healthStatus = getHealthStatus();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* System Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6 text-center">
          <Icon name="server" className={`w-12 h-12 mx-auto mb-2 text-${healthStatus.color}-500`} />
          <h3 className="text-lg font-bold text-neutral-darkest capitalize">{healthStatus.status}</h3>
          <p className="text-neutral-dark">System Status</p>
        </Card>
        
        <Card className="p-6 text-center">
          <Icon name="clock" className="w-12 h-12 text-blue-500 mx-auto mb-2" />
          <h3 className="text-lg font-bold text-neutral-darkest">{formatUptime(systemHealth.uptime)}</h3>
          <p className="text-neutral-dark">Uptime</p>
        </Card>
        
        <Card className="p-6 text-center">
          <Icon name="cpu" className="w-12 h-12 text-purple-500 mx-auto mb-2" />
          <h3 className="text-lg font-bold text-neutral-darkest">{systemHealth.memoryUsage}%</h3>
          <p className="text-neutral-dark">Memory Usage</p>
        </Card>
        
        <Card className="p-6 text-center">
          <Icon name="users" className="w-12 h-12 text-green-500 mx-auto mb-2" />
          <h3 className="text-lg font-bold text-neutral-darkest">{systemHealth.activeUsers}</h3>
          <p className="text-neutral-dark">Active Users</p>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
            <Icon name="chartBar" className="w-6 h-6 mr-2" />
            Performance Metrics
          </h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-neutral-dark">Memory Usage</span>
                <span className="text-sm font-medium">{systemHealth.memoryUsage}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${
                    systemHealth.memoryUsage > 80 ? 'bg-red-500' :
                    systemHealth.memoryUsage > 60 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${systemHealth.memoryUsage}%` }}
                ></div>
              </div>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-neutral-dark">Error Count (24h)</span>
              <span className={`text-lg font-bold ${
                systemHealth.errorCount > 10 ? 'text-red-500' :
                systemHealth.errorCount > 5 ? 'text-yellow-500' : 'text-green-500'
              }`}>
                {systemHealth.errorCount}
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-neutral-dark">Last Backup</span>
              <span className="text-sm text-neutral-darkest">
                {new Date(systemHealth.lastBackup).toLocaleDateString()}
              </span>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
            <Icon name="wrench" className="w-6 h-6 mr-2" />
            System Actions
          </h3>
          <div className="space-y-4">
            <Button
              onClick={refreshHealth}
              isLoading={isRefreshing}
              variant="outline"
              className="w-full"
            >
              <Icon name="arrowUpTray" className="w-4 h-4 mr-2" />
              Refresh Health Data
            </Button>
            
            <Button
              onClick={() => {
                // Simulate backup
                alert('Backup initiated successfully!');
              }}
              variant="outline"
              className="w-full"
            >
              <Icon name="cloudArrowUp" className="w-4 h-4 mr-2" />
              Create Backup
            </Button>
            
            <Button
              onClick={() => setShowResetConfirm(true)}
              variant="danger"
              className="w-full"
            >
              <Icon name="database" className="w-4 h-4 mr-2" />
              Reset to Demo Data
            </Button>
          </div>
        </Card>
      </div>

      {/* System Logs */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
          <Icon name="documentText" className="w-6 h-6 mr-2" />
          Recent System Logs
        </h3>
        <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-64 overflow-y-auto">
          <div className="space-y-1">
            <div>[{new Date().toISOString()}] INFO: System health check completed</div>
            <div>[{new Date(Date.now() - 60000).toISOString()}] INFO: User authentication successful</div>
            <div>[{new Date(Date.now() - 120000).toISOString()}] INFO: Database connection established</div>
            <div>[{new Date(Date.now() - 180000).toISOString()}] INFO: Content moderation scan completed</div>
            <div>[{new Date(Date.now() - 240000).toISOString()}] INFO: Backup process initiated</div>
            <div>[{new Date(Date.now() - 300000).toISOString()}] WARN: High memory usage detected (85%)</div>
            <div>[{new Date(Date.now() - 360000).toISOString()}] INFO: User session cleanup completed</div>
            <div>[{new Date(Date.now() - 420000).toISOString()}] INFO: System startup completed</div>
          </div>
        </div>
      </Card>

      {/* Reset Confirmation Modal */}
      {showResetConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="p-6 max-w-md mx-4">
            <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
              <Icon name="exclamationTriangle" className="w-6 h-6 text-red-500 mr-2" />
              Confirm Reset
            </h3>
            <p className="text-neutral-dark mb-6">
              Are you sure you want to reset all data to the initial demo state? 
              This action cannot be undone and will remove all current users, posts, and events.
            </p>
            <div className="flex space-x-3">
              <Button
                onClick={() => setShowResetConfirm(false)}
                variant="outline"
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleReset}
                variant="danger"
                className="flex-1"
              >
                Reset Data
              </Button>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};
