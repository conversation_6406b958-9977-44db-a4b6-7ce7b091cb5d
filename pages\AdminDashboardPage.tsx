import React, { useState, useEffect } from 'react';
import { PageContainer } from '../components/Layout';
import { useApp } from '../AppContext';
import { User, UserRole, Post, Event } from '../types';
import { Button, Input, Select, Card, Avatar, Icon, Modal, Tabs } from '../components';
import { AdminSiteSettings } from '../components/AdminSiteSettings';
import { AdminAnalytics } from '../components/AdminAnalytics';
import { AdminSystemHealth } from '../components/AdminSystemHealth';
import { AdminCommunication } from '../components/AdminCommunication';

const AdminDashboardPage: React.FC = () => {
  const {
    currentUser, users, posts, events,
    deleteUser, updateUserRole, toggleUserStatus,
    deletePost, deleteEvent, getUserStats
  } = useApp();

  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<UserRole | ''>('');
  const [statusFilter, setStatusFilter] = useState<'active' | 'inactive' | ''>('');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);

  // Redirect if not admin
  useEffect(() => {
    if (currentUser && currentUser.role !== UserRole.ADMIN) {
      window.location.href = '/dashboard';
    }
  }, [currentUser]);

  if (!currentUser || currentUser.role !== UserRole.ADMIN) {
    return (
      <PageContainer title="Access Denied">
        <Card className="p-8 text-center">
          <Icon name="exclamationTriangle" className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-neutral-darkest mb-2">Access Denied</h2>
          <p className="text-neutral-dark">You need administrator privileges to access this page.</p>
        </Card>
      </PageContainer>
    );
  }

  const stats = getUserStats();

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = !roleFilter || user.role === roleFilter;
    const matchesStatus = !statusFilter ||
                         (statusFilter === 'active' && user.isActive !== false) ||
                         (statusFilter === 'inactive' && user.isActive === false);

    return matchesSearch && matchesRole && matchesStatus;
  });

  const handleDeleteUser = async (user: User) => {
    setUserToDelete(user);
    setIsDeleteModalOpen(true);
  };

  const confirmDeleteUser = async () => {
    if (userToDelete) {
      await deleteUser(userToDelete.id);
      setIsDeleteModalOpen(false);
      setUserToDelete(null);
    }
  };

  const handleRoleChange = async (userId: string, newRole: UserRole) => {
    await updateUserRole(userId, newRole);
  };

  const handleToggleStatus = async (userId: string) => {
    await toggleUserStatus(userId);
  };

  const handleBulkAction = async (action: 'delete' | 'activate' | 'deactivate') => {
    for (const userId of selectedUsers) {
      if (action === 'delete') {
        await deleteUser(userId);
      } else if (action === 'activate' || action === 'deactivate') {
        const user = users.find(u => u.id === userId);
        if (user && ((action === 'activate' && user.isActive === false) ||
                    (action === 'deactivate' && user.isActive !== false))) {
          await toggleUserStatus(userId);
        }
      }
    }
    setSelectedUsers([]);
  };

  const StatsOverview = () => {
    const totalLikes = posts.reduce((sum, post) => sum + post.likes, 0);
    const totalRSVPs = events.reduce((sum, event) => sum + event.rsvps.length, 0);
    const now = new Date();
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const newUsersThisWeek = users.filter(user =>
      user.createdAt && new Date(user.createdAt) > lastWeek
    ).length;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-6 mb-8">
        <Card className="p-6 text-center hover:shadow-lg transition-shadow">
          <Icon name="users" className="w-12 h-12 text-primary mx-auto mb-2" />
          <h3 className="text-2xl font-bold text-neutral-darkest">{stats.totalUsers}</h3>
          <p className="text-neutral-dark">Total Users</p>
          <p className="text-xs text-green-600 mt-1">+{newUsersThisWeek} this week</p>
        </Card>
        <Card className="p-6 text-center hover:shadow-lg transition-shadow">
          <Icon name="userGroup" className="w-12 h-12 text-green-500 mx-auto mb-2" />
          <h3 className="text-2xl font-bold text-neutral-darkest">{stats.activeUsers}</h3>
          <p className="text-neutral-dark">Active Users</p>
          <p className="text-xs text-neutral-dark mt-1">{((stats.activeUsers / stats.totalUsers) * 100).toFixed(1)}% retention</p>
        </Card>
        <Card className="p-6 text-center hover:shadow-lg transition-shadow">
          <Icon name="photo" className="w-12 h-12 text-blue-500 mx-auto mb-2" />
          <h3 className="text-2xl font-bold text-neutral-darkest">{posts.length}</h3>
          <p className="text-neutral-dark">Total Posts</p>
          <p className="text-xs text-neutral-dark mt-1">{(posts.length / stats.totalUsers).toFixed(1)} per user</p>
        </Card>
        <Card className="p-6 text-center hover:shadow-lg transition-shadow">
          <Icon name="calendar" className="w-12 h-12 text-purple-500 mx-auto mb-2" />
          <h3 className="text-2xl font-bold text-neutral-darkest">{events.length}</h3>
          <p className="text-neutral-dark">Total Events</p>
          <p className="text-xs text-neutral-dark mt-1">{totalRSVPs} total RSVPs</p>
        </Card>
        <Card className="p-6 text-center hover:shadow-lg transition-shadow">
          <Icon name="heart" className="w-12 h-12 text-red-500 mx-auto mb-2" />
          <h3 className="text-2xl font-bold text-neutral-darkest">{totalLikes}</h3>
          <p className="text-neutral-dark">Total Likes</p>
          <p className="text-xs text-neutral-dark mt-1">{posts.length > 0 ? (totalLikes / posts.length).toFixed(1) : '0'} per post</p>
        </Card>
        <Card className="p-6 text-center hover:shadow-lg transition-shadow">
          <Icon name="sparkles" className="w-12 h-12 text-yellow-500 mx-auto mb-2" />
          <h3 className="text-2xl font-bold text-neutral-darkest">{posts.length > 0 ? (totalLikes / posts.length * 100).toFixed(0) : '0'}%</h3>
          <p className="text-neutral-dark">Engagement</p>
          <p className="text-xs text-neutral-dark mt-1">Avg engagement rate</p>
        </Card>
      </div>
    );
  };

  const Analytics = () => (
    <div className="space-y-6">
      {/* User Analytics */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
          <Icon name="chartBar" className="w-6 h-6 mr-2" />
          User Analytics
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Object.entries(stats.usersByRole).map(([role, count]) => (
            <div key={role} className="text-center p-4 bg-neutral-light/30 rounded-lg">
              <div className="text-2xl font-bold text-neutral-darkest">{count}</div>
              <div className="text-sm text-neutral-dark">{role}s</div>
            </div>
          ))}
        </div>
      </Card>

      {/* Content Analytics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h4 className="text-lg font-semibold text-neutral-darkest mb-4">Content Overview</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-neutral-dark">Total Posts:</span>
              <span className="font-semibold">{posts.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-dark">Total Events:</span>
              <span className="font-semibold">{events.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-dark">Active Users:</span>
              <span className="font-semibold">{stats.activeUsers}</span>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h4 className="text-lg font-semibold text-neutral-darkest mb-4">Recent Activity</h4>
          <div className="space-y-3">
            <div className="text-sm text-neutral-dark">
              Latest Post: {posts.length > 0 ? new Date(posts[0].createdAt).toLocaleDateString() : 'None'}
            </div>
            <div className="text-sm text-neutral-dark">
              Latest Event: {events.length > 0 ? new Date(events[0].createdAt || events[0].dateTime).toLocaleDateString() : 'None'}
            </div>
            <div className="text-sm text-neutral-dark">
              Latest User: {users.length > 1 ? new Date(users[users.length - 1].createdAt || '').toLocaleDateString() : 'None'}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );

  const ContentModeration = () => (
    <div className="space-y-6">
      {/* Posts Management */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
          <Icon name="photo" className="w-6 h-6 mr-2" />
          Posts Management
        </h3>
        <div className="space-y-4">
          {posts.slice(0, 5).map((post) => {
            const author = users.find(u => u.id === post.userId);
            return (
              <div key={post.id} className="flex items-center justify-between p-4 border border-neutral-light rounded-lg">
                <div className="flex items-center space-x-4">
                  <img src={post.mediaUrl} alt={post.title} className="w-16 h-16 object-cover rounded" />
                  <div>
                    <h4 className="font-medium text-neutral-darkest">{post.title}</h4>
                    <p className="text-sm text-neutral-dark">by {author?.displayName}</p>
                    <p className="text-xs text-neutral-dark">{new Date(post.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button size="sm" variant="outline" onClick={() => window.open(`/explore`, '_blank')}>
                    <Icon name="eye" className="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="danger" onClick={() => deletePost(post.id)}>
                    <Icon name="trash" className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      </Card>

      {/* Events Management */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
          <Icon name="calendar" className="w-6 h-6 mr-2" />
          Events Management
        </h3>
        <div className="space-y-4">
          {events.slice(0, 5).map((event) => {
            const educator = users.find(u => u.id === event.educatorId);
            return (
              <div key={event.id} className="flex items-center justify-between p-4 border border-neutral-light rounded-lg">
                <div>
                  <h4 className="font-medium text-neutral-darkest">{event.title}</h4>
                  <p className="text-sm text-neutral-dark">by {educator?.displayName}</p>
                  <p className="text-xs text-neutral-dark">{new Date(event.dateTime).toLocaleDateString()}</p>
                </div>
                <div className="flex space-x-2">
                  <Button size="sm" variant="outline" onClick={() => window.open(`/admin/event/${event.id}`, '_blank')}>
                    <Icon name="eye" className="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="danger" onClick={() => deleteEvent(event.id)}>
                    <Icon name="trash" className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      </Card>
    </div>
  );

  const UserManagement = () => (
    <div>
      {/* Filters and Search */}
      <div className="mb-6 flex flex-col md:flex-row gap-4">
        <Input
          placeholder="Search users..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          icon={<Icon name="magnifyingGlass" className="w-5 h-5 text-neutral-dark" />}
          className="flex-1"
        />
        <Select
          value={roleFilter}
          onChange={(e) => setRoleFilter(e.target.value as UserRole | '')}
          options={[
            { value: '', label: 'All Roles' },
            { value: UserRole.ARTIST, label: 'Artists' },
            { value: UserRole.EDUCATOR, label: 'Educators' },
            { value: UserRole.FAN, label: 'Fans' },
            { value: UserRole.ADMIN, label: 'Admins' }
          ]}
        />
        <Select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value as 'active' | 'inactive' | '')}
          options={[
            { value: '', label: 'All Status' },
            { value: 'active', label: 'Active' },
            { value: 'inactive', label: 'Inactive' }
          ]}
        />
      </div>

      {/* Bulk Actions */}
      {selectedUsers.length > 0 && (
        <div className="mb-4 p-4 bg-blue-50 rounded-lg flex items-center justify-between">
          <span className="text-blue-800">{selectedUsers.length} users selected</span>
          <div className="space-x-2">
            <Button size="sm" variant="outline" onClick={() => handleBulkAction('activate')}>
              Activate
            </Button>
            <Button size="sm" variant="outline" onClick={() => handleBulkAction('deactivate')}>
              Deactivate
            </Button>
            <Button size="sm" variant="danger" onClick={() => handleBulkAction('delete')}>
              Delete
            </Button>
          </div>
        </div>
      )}

      {/* Users Table */}
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-neutral-light">
              <tr>
                <th className="px-4 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedUsers(filteredUsers.map(u => u.id));
                      } else {
                        setSelectedUsers([]);
                      }
                    }}
                  />
                </th>
                <th className="px-4 py-3 text-left font-semibold text-neutral-darkest">User</th>
                <th className="px-4 py-3 text-left font-semibold text-neutral-darkest">Role</th>
                <th className="px-4 py-3 text-left font-semibold text-neutral-darkest">Status</th>
                <th className="px-4 py-3 text-left font-semibold text-neutral-darkest">Joined</th>
                <th className="px-4 py-3 text-left font-semibold text-neutral-darkest">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.map((user) => (
                <tr key={user.id} className="border-b border-neutral-light hover:bg-neutral-light/30">
                  <td className="px-4 py-3">
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(user.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedUsers([...selectedUsers, user.id]);
                        } else {
                          setSelectedUsers(selectedUsers.filter(id => id !== user.id));
                        }
                      }}
                    />
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex items-center">
                      <Avatar src={user.profilePhotoUrl} alt={user.displayName} size="sm" className="mr-3" />
                      <div>
                        <div className="font-medium text-neutral-darkest">{user.displayName}</div>
                        <div className="text-sm text-neutral-dark">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <Select
                      value={user.role}
                      onChange={(e) => handleRoleChange(user.id, e.target.value as UserRole)}
                      options={[
                        { value: UserRole.ARTIST, label: 'Artist' },
                        { value: UserRole.EDUCATOR, label: 'Educator' },
                        { value: UserRole.FAN, label: 'Fan' },
                        { value: UserRole.ADMIN, label: 'Admin' }
                      ]}
                      className="text-sm"
                    />
                  </td>
                  <td className="px-4 py-3">
                    <button
                      onClick={() => handleToggleStatus(user.id)}
                      className={`px-3 py-1 rounded-full text-xs font-medium ${
                        user.isActive !== false
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {user.isActive !== false ? 'Active' : 'Inactive'}
                    </button>
                  </td>
                  <td className="px-4 py-3 text-sm text-neutral-dark">
                    {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.open(`/admin/member/${user.id}`, '_blank')}
                      >
                        <Icon name="eye" className="w-4 h-4" />
                      </Button>
                      {user.id !== currentUser.id && (
                        <Button
                          size="sm"
                          variant="danger"
                          onClick={() => handleDeleteUser(user)}
                        >
                          <Icon name="trash" className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );

  return (
    <PageContainer title="Admin Dashboard">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Icon name="shield" className="w-8 h-8 text-primary mr-3" />
          <div>
            <h1 className="text-3xl font-bold text-neutral-darkest">Admin Dashboard</h1>
            <p className="text-neutral-dark">Complete control center for site management and administration</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <div className="text-right">
            <p className="text-sm text-neutral-dark">Welcome back,</p>
            <p className="font-semibold text-neutral-darkest">{currentUser?.displayName}</p>
          </div>
          <Avatar src={currentUser?.profilePhotoUrl} alt={currentUser?.displayName} size="md" />
        </div>
      </div>

      <StatsOverview />

      <Tabs
        tabs={[
          { name: 'Overview', content: <Analytics /> },
          { name: 'User Management', content: <UserManagement /> },
          { name: 'Content Moderation', content: <ContentModeration /> },
          { name: 'Site Settings', content: <AdminSiteSettings /> },
          { name: 'Analytics & Reports', content: <AdminAnalytics /> },
          { name: 'System Health', content: <AdminSystemHealth /> },
          { name: 'Communication', content: <AdminCommunication /> }
        ]}
      />

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Confirm Delete User"
      >
        {userToDelete && (
          <div>
            <p className="mb-4">
              Are you sure you want to delete <strong>{userToDelete.displayName}</strong>?
              This action cannot be undone and will also delete all their posts and events.
            </p>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsDeleteModalOpen(false)}>
                Cancel
              </Button>
              <Button variant="danger" onClick={confirmDeleteUser}>
                Delete User
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </PageContainer>
  );
};

export default AdminDashboardPage;
