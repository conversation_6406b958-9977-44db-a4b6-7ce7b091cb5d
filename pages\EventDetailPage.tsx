import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { PageContainer } from '../components/Layout';
import { useApp } from '../AppContext';
import { Event, User, UserRole } from '../types';
import { Card, Button, Icon, Input, Textarea, Select, Modal, Avatar } from '../components';

const EventDetailPage: React.FC = () => {
  const { eventId } = useParams<{ eventId: string }>();
  const { events, users, currentUser, getUserById } = useApp();
  const [event, setEvent] = useState<Event | null>(null);
  const [educator, setEducator] = useState<User | null>(null);
  const [isEditing, setIsEditing] = useState<string | null>(null);
  const [editData, setEditData] = useState<Partial<Event>>({});
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (eventId) {
      const foundEvent = events.find(e => e.id === eventId);
      setEvent(foundEvent || null);
      setEditData(foundEvent || {});
      
      if (foundEvent) {
        const foundEducator = getUserById(foundEvent.educatorId);
        setEducator(foundEducator);
      }
    }
  }, [eventId, events, getUserById]);

  const handleSaveEdit = async (section: string) => {
    if (!event || !editData) return;
    
    // In a real app, you'd have an updateEvent function in the context
    // For now, we'll simulate the update
    console.log('Saving event updates:', editData);
    setIsEditing(null);
    // Update local state to reflect changes
    setEvent(prev => prev ? { ...prev, ...editData } : null);
  };

  const handleCancelEdit = () => {
    setEditData(event || {});
    setIsEditing(null);
  };

  if (!event) {
    return <PageContainer title="Event Not Found"><p>The event could not be found.</p></PageContainer>;
  }

  const eventDate = new Date(event.dateTime);
  const isUpcoming = eventDate > new Date();

  const EditableSection: React.FC<{ 
    title: string; 
    sectionKey: string; 
    children: React.ReactNode; 
    editContent?: React.ReactNode 
  }> = ({ title, sectionKey, children, editContent }) => (
    <Card className="p-6 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold text-neutral-darkest">{title}</h3>
        {currentUser?.role === UserRole.ADMIN && (
          <div className="flex space-x-2">
            {isEditing === sectionKey ? (
              <>
                <Button size="sm" onClick={() => handleSaveEdit(sectionKey)} variant="primary">
                  <Icon name="check" className="w-4 h-4" />
                </Button>
                <Button size="sm" onClick={handleCancelEdit} variant="outline">
                  <Icon name="x" className="w-4 h-4" />
                </Button>
              </>
            ) : (
              <Button size="sm" onClick={() => setIsEditing(sectionKey)} variant="outline">
                <Icon name="pencil" className="w-4 h-4" />
              </Button>
            )}
          </div>
        )}
      </div>
      {isEditing === sectionKey && editContent ? editContent : children}
    </Card>
  );

  const BasicInfoSection = () => (
    <EditableSection 
      title="Event Information" 
      sectionKey="basicInfo"
      editContent={
        <div className="space-y-4">
          <Input
            label="Event Title"
            value={editData.title || ''}
            onChange={(e) => setEditData(prev => ({ ...prev, title: e.target.value }))}
          />
          <Input
            label="Date & Time"
            type="datetime-local"
            value={editData.dateTime ? new Date(editData.dateTime).toISOString().slice(0, 16) : ''}
            onChange={(e) => setEditData(prev => ({ ...prev, dateTime: e.target.value }))}
          />
          <Select
            label="Format"
            value={editData.format || ''}
            onChange={(e) => setEditData(prev => ({ ...prev, format: e.target.value as 'virtual' | 'in-person' }))}
            options={[
              { value: 'virtual', label: 'Virtual Event' },
              { value: 'in-person', label: 'In-Person Event' }
            ]}
          />
          <Input
            label="Location/Link"
            value={editData.locationOrLink || ''}
            onChange={(e) => setEditData(prev => ({ ...prev, locationOrLink: e.target.value }))}
          />
          <Input
            label="Price"
            value={editData.price || ''}
            onChange={(e) => setEditData(prev => ({ ...prev, price: e.target.value }))}
            placeholder="Free, $25, etc."
          />
        </div>
      }
    >
      <div className="space-y-4">
        <div>
          <h4 className="text-2xl font-bold text-neutral-darkest mb-2">{event.title}</h4>
          <div className="flex items-center text-sm text-neutral-dark mb-2">
            <Icon name="calendarDays" className="w-5 h-5 mr-2 text-primary" />
            <span>{eventDate.toLocaleDateString()} at {eventDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
          </div>
          <div className="flex items-center text-sm text-neutral-dark mb-2">
            <Icon name="tag" className="w-5 h-5 mr-2 text-secondary" />
            <span>{event.format === 'virtual' ? '🌐 Virtual Event' : '📍 In-Person Event'}</span>
          </div>
          <div className="flex items-center text-sm text-neutral-dark mb-2">
            <Icon name="mapPin" className="w-5 h-5 mr-2 text-accent-dark" />
            <span>{event.locationOrLink}</span>
          </div>
          {event.price && (
            <div className="flex items-center text-sm text-neutral-dark mb-2">
              <Icon name="tag" className="w-5 h-5 mr-2 text-green-600" />
              <span className="font-medium">{event.price}</span>
            </div>
          )}
          <div className="flex items-center text-sm text-neutral-dark">
            <Icon name="users" className="w-5 h-5 mr-2 text-purple-600" />
            <span>{event.rsvps.length} RSVPs</span>
          </div>
        </div>
      </div>
    </EditableSection>
  );

  const DescriptionSection = () => (
    <EditableSection 
      title="Event Description" 
      sectionKey="description"
      editContent={
        <Textarea
          label="Description"
          value={editData.description || ''}
          onChange={(e) => setEditData(prev => ({ ...prev, description: e.target.value }))}
          rows={6}
        />
      }
    >
      <p className="text-neutral-dark whitespace-pre-wrap leading-relaxed">
        {event.description}
      </p>
    </EditableSection>
  );

  const EducatorSection = () => (
    <Card className="p-6 mb-6">
      <h3 className="text-xl font-semibold text-neutral-darkest mb-4">Event Host</h3>
      {educator ? (
        <div className="flex items-center space-x-4">
          <Avatar src={educator.profilePhotoUrl} alt={educator.displayName} size="md" />
          <div>
            <h4 className="font-semibold text-neutral-darkest">{educator.displayName}</h4>
            <p className="text-sm text-neutral-dark">{educator.role}</p>
            {educator.location && (
              <p className="text-xs text-neutral-dark">
                <Icon name="mapPin" className="inline w-3 h-3 mr-1" />
                {educator.location}
              </p>
            )}
          </div>
          <div className="ml-auto">
            <Link to={`/admin/member/${educator.id}`}>
              <Button size="sm" variant="outline">
                <Icon name="eye" className="w-4 h-4 mr-2" />
                View Profile
              </Button>
            </Link>
          </div>
        </div>
      ) : (
        <p className="text-neutral-dark">Educator information not available</p>
      )}
    </Card>
  );

  const RSVPSection = () => (
    <Card className="p-6 mb-6">
      <h3 className="text-xl font-semibold text-neutral-darkest mb-4">
        RSVPs ({event.rsvps.length})
      </h3>
      {event.rsvps.length > 0 ? (
        <div className="space-y-3">
          {event.rsvps.slice(0, 10).map(userId => {
            const user = getUserById(userId);
            return user ? (
              <div key={userId} className="flex items-center justify-between p-3 border border-neutral-light rounded-lg">
                <div className="flex items-center space-x-3">
                  <Avatar src={user.profilePhotoUrl} alt={user.displayName} size="sm" />
                  <div>
                    <p className="font-medium text-neutral-darkest">{user.displayName}</p>
                    <p className="text-sm text-neutral-dark">{user.role}</p>
                  </div>
                </div>
                <Link to={`/admin/member/${user.id}`}>
                  <Button size="sm" variant="outline">
                    <Icon name="eye" className="w-4 h-4" />
                  </Button>
                </Link>
              </div>
            ) : null;
          })}
          {event.rsvps.length > 10 && (
            <p className="text-sm text-neutral-dark text-center">
              And {event.rsvps.length - 10} more...
            </p>
          )}
        </div>
      ) : (
        <p className="text-neutral-dark">No RSVPs yet</p>
      )}
    </Card>
  );

  return (
    <PageContainer title={`${event.title} - Event Details`}>
      {/* Header with back button */}
      <div className="mb-6 flex items-center justify-between">
        <Link to="/admin" className="flex items-center text-primary hover:text-primary-dark">
          <Icon name="arrowLeft" className="w-5 h-5 mr-2" />
          Back to Admin Dashboard
        </Link>
        <div className="flex items-center space-x-3">
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            isUpcoming ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          }`}>
            {isUpcoming ? 'Upcoming' : 'Past Event'}
          </div>
          <Button onClick={() => setIsModalOpen(true)} variant="outline">
            <Icon name="eye" className="w-4 h-4 mr-2" />
            View Public Event
          </Button>
        </div>
      </div>

      {/* Event Details */}
      <div className="space-y-6">
        <BasicInfoSection />
        <DescriptionSection />
        <EducatorSection />
        <RSVPSection />
      </div>

      {/* Modal for public event view */}
      <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} title="Public Event View" size="lg">
        <p className="mb-4 text-neutral-dark">This will open the public events page in a new tab.</p>
        <div className="flex justify-end space-x-3">
          <Button onClick={() => setIsModalOpen(false)} variant="outline">Cancel</Button>
          <Button onClick={() => {
            window.open('/events', '_blank');
            setIsModalOpen(false);
          }}>
            Open Events Page
          </Button>
        </div>
      </Modal>
    </PageContainer>
  );
};

export default EventDetailPage;
