import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { PageContainer } from '../components/Layout';
import { useApp } from '../AppContext';
import { User, UserRole, Post, Event } from '../types';
import { Avatar, Banner, Card, Tag, FeedItem, EventItem, ProfileSection, Button, Icon, SocialLinkItem, Tabs, Input, Textarea, Select, Modal } from '../components';

const MemberDetailPage: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const { getUserById, getPosts, getEvents, currentUser, updateUserProfile } = useApp();
  const [profileUser, setProfileUser] = useState<User | null>(null);
  const [userContent, setUserContent] = useState<{ posts: Post[]; events: Event[] }>({ posts: [], events: [] });
  const [isEditing, setIsEditing] = useState<string | null>(null); // Track which section is being edited
  const [editData, setEditData] = useState<Partial<User>>({});
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (userId) {
      const fetchedUser = getUserById(userId);
      setProfileUser(fetchedUser);
      setEditData(fetchedUser || {});
      if (fetchedUser) {
        if (fetchedUser.role === UserRole.ARTIST) {
          setUserContent(prev => ({ ...prev, posts: getPosts({ userId: fetchedUser.id }) }));
        }
        if (fetchedUser.role === UserRole.EDUCATOR) {
          setUserContent(prev => ({ ...prev, events: getEvents({ educatorId: fetchedUser.id }) }));
        }
      }
    }
  }, [userId, getUserById, getPosts, getEvents]);

  const handleSaveEdit = async (section: string) => {
    if (!profileUser || !editData) return;

    const success = await updateUserProfile(profileUser.id, editData);
    if (success) {
      setProfileUser(success);
      setIsEditing(null);
    }
  };

  const handleCancelEdit = () => {
    setEditData(profileUser || {});
    setIsEditing(null);
  };

  if (!profileUser) {
    return <PageContainer title="Member Not Found"><p>The member profile could not be found.</p></PageContainer>;
  }

  const EditableSection: React.FC<{
    title: string;
    sectionKey: string;
    children: React.ReactNode;
    editContent?: React.ReactNode
  }> = ({ title, sectionKey, children, editContent }) => (
    <Card className="p-6 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold text-neutral-darkest">{title}</h3>
        {currentUser?.role === UserRole.ADMIN && (
          <div className="flex space-x-2">
            {isEditing === sectionKey ? (
              <>
                <Button size="sm" onClick={() => handleSaveEdit(sectionKey)} variant="primary">
                  <Icon name="check" className="w-4 h-4" />
                </Button>
                <Button size="sm" onClick={handleCancelEdit} variant="outline">
                  <Icon name="x" className="w-4 h-4" />
                </Button>
              </>
            ) : (
              <Button size="sm" onClick={() => setIsEditing(sectionKey)} variant="outline">
                <Icon name="pencil" className="w-4 h-4" />
              </Button>
            )}
          </div>
        )}
      </div>
      {isEditing === sectionKey && editContent ? editContent : children}
    </Card>
  );

  const BasicInfoSection = () => (
    <EditableSection
      title="Basic Information"
      sectionKey="basicInfo"
      editContent={
        <div className="space-y-4">
          <Input
            label="Display Name"
            value={editData.displayName || ''}
            onChange={(e) => setEditData(prev => ({ ...prev, displayName: e.target.value }))}
          />
          <Input
            label="Email"
            value={editData.email || ''}
            onChange={(e) => setEditData(prev => ({ ...prev, email: e.target.value }))}
          />
          <Input
            label="Location"
            value={editData.location || ''}
            onChange={(e) => setEditData(prev => ({ ...prev, location: e.target.value }))}
          />
          <Select
            label="Role"
            value={editData.role || ''}
            onChange={(e) => setEditData(prev => ({ ...prev, role: e.target.value as UserRole }))}
            options={[
              { value: UserRole.ARTIST, label: 'Artist' },
              { value: UserRole.EDUCATOR, label: 'Educator' },
              { value: UserRole.FAN, label: 'Fan' },
              { value: UserRole.ADMIN, label: 'Admin' }
            ]}
          />
        </div>
      }
    >
      <div className="space-y-3">
        <div><strong>Display Name:</strong> {profileUser.displayName}</div>
        <div><strong>Email:</strong> {profileUser.email}</div>
        <div><strong>Role:</strong> {profileUser.role}</div>
        <div><strong>Location:</strong> {profileUser.location || 'Not specified'}</div>
        <div><strong>Member Since:</strong> {profileUser.createdAt ? new Date(profileUser.createdAt).toLocaleDateString() : 'Unknown'}</div>
        <div><strong>Status:</strong>
          <span className={`ml-2 px-2 py-1 rounded-full text-xs ${
            profileUser.isActive !== false ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {profileUser.isActive !== false ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>
    </Card>
  );

  const BioSection = () => (
    <EditableSection
      title="Bio & Description"
      sectionKey="bio"
      editContent={
        <Textarea
          label="Bio"
          value={editData.bio || ''}
          onChange={(e) => setEditData(prev => ({ ...prev, bio: e.target.value }))}
          rows={4}
        />
      }
    >
      <p className="text-neutral-dark whitespace-pre-wrap">
        {profileUser.bio || 'No bio provided.'}
      </p>
    </EditableSection>
  );

  const TagsSection = () => (
    <EditableSection
      title="Tags & Interests"
      sectionKey="tags"
      editContent={
        <Input
          label="Tags (comma-separated)"
          value={editData.tags?.join(', ') || ''}
          onChange={(e) => setEditData(prev => ({
            ...prev,
            tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
          }))}
          placeholder="music, art, jazz, painting..."
        />
      }
    >
      <div className="flex flex-wrap gap-2">
        {profileUser.tags.length > 0 ? (
          profileUser.tags.map(tag => <Tag key={tag}>{tag}</Tag>)
        ) : (
          <p className="text-neutral-dark">No tags specified</p>
        )}
      </div>
    </EditableSection>
  );

  const SocialLinksSection = () => (
    <EditableSection
      title="Social Links"
      sectionKey="socialLinks"
      editContent={
        <div className="space-y-4">
          <Input
            label="Instagram"
            value={editData.socialLinks?.instagram || ''}
            onChange={(e) => setEditData(prev => ({
              ...prev,
              socialLinks: { ...prev.socialLinks, instagram: e.target.value }
            }))}
          />
          <Input
            label="TikTok"
            value={editData.socialLinks?.tiktok || ''}
            onChange={(e) => setEditData(prev => ({
              ...prev,
              socialLinks: { ...prev.socialLinks, tiktok: e.target.value }
            }))}
          />
          <Input
            label="YouTube"
            value={editData.socialLinks?.youtube || ''}
            onChange={(e) => setEditData(prev => ({
              ...prev,
              socialLinks: { ...prev.socialLinks, youtube: e.target.value }
            }))}
          />
          <Input
            label="SoundCloud"
            value={editData.socialLinks?.soundcloud || ''}
            onChange={(e) => setEditData(prev => ({
              ...prev,
              socialLinks: { ...prev.socialLinks, soundcloud: e.target.value }
            }))}
          />
          <Input
            label="Spotify"
            value={editData.socialLinks?.spotify || ''}
            onChange={(e) => setEditData(prev => ({
              ...prev,
              socialLinks: { ...prev.socialLinks, spotify: e.target.value }
            }))}
          />
        </div>
      }
    >
      <div className="flex flex-wrap gap-4">
        <SocialLinkItem platform="Instagram" url={profileUser.socialLinks?.instagram} iconName="link" />
        <SocialLinkItem platform="TikTok" url={profileUser.socialLinks?.tiktok} iconName="link" />
        <SocialLinkItem platform="YouTube" url={profileUser.socialLinks?.youtube} iconName="link" />
        <SocialLinkItem platform="SoundCloud" url={profileUser.socialLinks?.soundcloud} iconName="link" />
        <SocialLinkItem platform="Spotify" url={profileUser.socialLinks?.spotify} iconName="link" />
        {!profileUser.socialLinks || Object.values(profileUser.socialLinks).every(link => !link) && (
          <p className="text-neutral-dark">No social links provided</p>
        )}
      </div>
    </EditableSection>
  );

  const RoleSpecificSection = () => {
    if (profileUser.role === UserRole.ARTIST) {
      return (
        <EditableSection
          title="Artist Information"
          sectionKey="artistInfo"
          editContent={
            <div className="space-y-4">
              <Textarea
                label="Artist Statement"
                value={editData.artistStatement || ''}
                onChange={(e) => setEditData(prev => ({ ...prev, artistStatement: e.target.value }))}
                rows={4}
              />
              <Input
                label="Genres (comma-separated)"
                value={editData.genre?.join(', ') || ''}
                onChange={(e) => setEditData(prev => ({
                  ...prev,
                  genre: e.target.value.split(',').map(g => g.trim()).filter(g => g)
                }))}
                placeholder="Jazz, Blues, Classical..."
              />
            </div>
          }
        >
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold text-neutral-darkest mb-2">Artist Statement</h4>
              <p className="text-neutral-dark whitespace-pre-wrap">
                {profileUser.artistStatement || 'No artist statement provided.'}
              </p>
            </div>
            {profileUser.genre && profileUser.genre.length > 0 && (
              <div>
                <h4 className="font-semibold text-neutral-darkest mb-2">Genres</h4>
                <div className="flex flex-wrap gap-2">
                  {profileUser.genre.map(genre => <Tag key={genre}>{genre}</Tag>)}
                </div>
              </div>
            )}
            <div>
              <h4 className="font-semibold text-neutral-darkest mb-2">Portfolio</h4>
              <p className="text-sm text-neutral-dark">{userContent.posts.length} posts</p>
            </div>
          </div>
        </EditableSection>
      );
    }

    if (profileUser.role === UserRole.EDUCATOR) {
      return (
        <EditableSection
          title="Educator Information"
          sectionKey="educatorInfo"
          editContent={
            <div className="space-y-4">
              <p className="text-sm text-neutral-dark">
                Lesson management will be available in a future update.
              </p>
            </div>
          }
        >
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold text-neutral-darkest mb-2">Events & Workshops</h4>
              <p className="text-sm text-neutral-dark">{userContent.events.length} events created</p>
            </div>
            {profileUser.lessonsOffered && profileUser.lessonsOffered.length > 0 && (
              <div>
                <h4 className="font-semibold text-neutral-darkest mb-2">Lessons Offered</h4>
                <div className="space-y-2">
                  {profileUser.lessonsOffered.map(lesson => (
                    <div key={lesson.id} className="p-3 border border-neutral-light rounded-lg">
                      <h5 className="font-medium">{lesson.title}</h5>
                      <p className="text-sm text-neutral-dark">{lesson.description}</p>
                      {lesson.duration && <p className="text-xs text-neutral-dark">Duration: {lesson.duration}</p>}
                      {lesson.price && <p className="text-xs text-neutral-dark">Price: {lesson.price}</p>}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </EditableSection>
      );
    }

    if (profileUser.role === UserRole.FAN) {
      return (
        <EditableSection
          title="Fan Activity"
          sectionKey="fanInfo"
          editContent={
            <div className="space-y-4">
              <p className="text-sm text-neutral-dark">
                Fan activity data is read-only.
              </p>
            </div>
          }
        >
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold text-neutral-darkest mb-2">Following</h4>
              <p className="text-sm text-neutral-dark">
                {profileUser.followedArtistIds?.length || 0} artists followed
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-neutral-darkest mb-2">Saved Content</h4>
              <p className="text-sm text-neutral-dark">
                {profileUser.savedPostIds?.length || 0} posts saved
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-neutral-darkest mb-2">Event RSVPs</h4>
              <p className="text-sm text-neutral-dark">
                {profileUser.rsvpedEventIds?.length || 0} events RSVP'd
              </p>
            </div>
          </div>
        </EditableSection>
      );
    }

    return null;
  };

  return (
    <PageContainer title={`${profileUser.displayName} - Member Details`}>
      {/* Header with back button */}
      <div className="mb-6 flex items-center justify-between">
        <Link to="/admin" className="flex items-center text-primary hover:text-primary-dark">
          <Icon name="arrowLeft" className="w-5 h-5 mr-2" />
          Back to Admin Dashboard
        </Link>
        <div className="flex items-center space-x-3">
          <Button onClick={() => setIsModalOpen(true)} variant="outline">
            <Icon name="eye" className="w-4 h-4 mr-2" />
            View Public Profile
          </Button>
        </div>
      </div>

      {/* Profile Header */}
      <Banner src={profileUser.bannerImageUrl} alt={`${profileUser.displayName} banner`} />
      <div className="-mt-12 sm:-mt-16 md:-mt-20 relative z-10 px-4">
        <Card className="p-6">
          <div className="flex flex-col sm:flex-row items-center sm:items-start">
            <Avatar src={profileUser.profilePhotoUrl} alt={profileUser.displayName} size="lg" className="mb-4 sm:mb-0 sm:mr-6" />
            <div className="text-center sm:text-left flex-grow">
              <h1 className="text-3xl font-bold text-neutral-darkest">{profileUser.displayName}</h1>
              <p className="text-lg text-primary">{profileUser.role}</p>
              {profileUser.location && (
                <p className="text-sm text-neutral-dark mt-1">
                  <Icon name="mapPin" className="inline w-4 h-4 mr-1" />
                  {profileUser.location}
                </p>
              )}
            </div>
          </div>
        </Card>
      </div>

      {/* Editable Sections */}
      <div className="mt-8 space-y-6">
        <BasicInfoSection />
        <BioSection />
        <TagsSection />
        <SocialLinksSection />
        <RoleSpecificSection />
      </div>

      {/* Modal for public profile view */}
      <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} title="Public Profile View" size="lg">
        <p className="mb-4 text-neutral-dark">This will open the public profile in a new tab.</p>
        <div className="flex justify-end space-x-3">
          <Button onClick={() => setIsModalOpen(false)} variant="outline">Cancel</Button>
          <Button onClick={() => {
            window.open(`/artist/${profileUser.id}`, '_blank');
            setIsModalOpen(false);
          }}>
            Open Public Profile
          </Button>
        </div>
      </Modal>
    </PageContainer>
  );
};

export default MemberDetailPage;
